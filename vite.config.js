import { fileURLToPath, URL } from "node:url";
import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";

// https://vitejs.dev/config/
export default defineConfig({
  base:"./",
  plugins: [vue()],
  resolve: {
    alias: {
      "@": fileURLToPath(new URL("./src", import.meta.url)),
    },
  },
  css: {
    preprocessorOptions: {
      less: {
        modifyVars: {
          themeColor:'#6FBECE',
          "primary-color": "rgba(34, 34, 34, 0.8)", 
          "secondary-color": "rgba(34, 34, 34, 0.65)",
          "primary-1": "#f5f5f5", //全局hover背景色
          "link-color": "#4E729F", //链接颜色
          "body-background": "#ffffff",
          "success-color": "#33BE4F", // 成功色
          "error-color": "#EA0C28", // 错误色
        },
        javascriptEnabled: true,
      },
    },
  },
  optimizeDeps: {
   
  },
  build: {
    cssMinify: false,
  },
  devServer: {
    allowedHosts: ["pc.ssnj.com"],
    // proxy: {
    //   '/': {
    //     target: 'https://gateway-beta.mingwork.com',
    //     ws: true,
    //     pathRewrite: {
    //       '^/': '/'
    //     }
    //   }
    // }
  },
  server: {
    open: true,
    port: 3006,
    host: "0.0.0.0",
  },
});
