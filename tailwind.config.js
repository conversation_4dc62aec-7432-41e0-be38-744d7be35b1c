module.exports = {
  purge: [],
  darkMode: false, // or 'media' or 'class'
  // important: true,
  content: [
    "./src/views/**/*.{js,jsx,ts,tsx}",
    "./src/components/**/*.{js,jsx,ts,tsx}",
  ],
  theme: {
    screens: {
      sm: "576px",
      md: "768px",
      lg: "992px",
      xl: "1200px",
      xxl: "1600px",
    },
    extend: {
      height: {
        banner: "400px",
        "banner-phone": "140px",
      },
      lineHeight: {
        5.5: "1.375rem", // 22px
        12: "3rem", //48px
        11:'2.75rem',
      },
      spacing: {
        4.5:'1.125rem',
        7.5:'1.875rem',
        12.5: "3.25rem",
        25: "6.25rem",
        15: "3.75rem",
        17: "4.25rem",
        18: "4.5rem",
        27:'6.75rem',
        30: "7.5rem",
        34: "8.5rem",
        54: "13.5rem",
      
      },

      fontSize: {
        "2.5xl": "1.75rem",
        "3.5xl": "2rem",
      },
      zIndex: {
        "1000": 1010,
      },
    },
    colors: {
      title: "#222222",
      "primary-text": "rgba(34, 34, 34, 0.8)",
      "secondar-text": "rgba(34, 34, 34, 0.65)",
      "third-text": "rgba(34, 34, 34, 0.4)",
      disable: "",
      border: "rgba(0, 0, 0, 0.15)",
      dividers: "rgba(0, 0, 0, 0.06)",
      background: "rgba(0, 0, 0, 0.04)",
      "00A0B8": "#00A0B8",
      ff: "#ffffff",
      f5: "#f5f5f5",
      59: "#595959",
      d9: "#d9d9d9",
      14: "#141414",
      99: "#999999",
      ea0c28: "#ea0c28",
      success: "#33BE4F",
      warn: "#ea0c28",
      wait: "#FD750B",
      "red-price": "#ea0c28",
      a0: "#a0a0a0",
      "9b": "#9b9b9b",
      "959EC3": "#959EC3",
      transparent: "transparent",
      f5f7f7:"#f5f7f7"
    },
    boxShadow: {
      hover20: "0px 0px 20px 0px rgba(0, 0, 0, 0.2)",
      "20": "0px 0px 20px 0px rgba(0, 0, 0, 0)",
    },
  },
  extend: {
    // backgroundImage: {
    //   // card: "url('../../assets/service/bg-card.png')",
    //   banner: "url('../assets/home-new/banner.jpg')",
    //   "banner-phone": "url('../assets/home-new/banner-phone.png')",
    // },
    spacing: {
      25: "6.25rem",
      17: "4.25rem",
      18: "4.5rem",
      30: "7.5rem",
      34: "8.5rem",
      54: "13.5rem",
    },
    colors: {
      f5: "#f5f5f5",
      59: "#595959",
      d9: "#d9d9d9",
    },
  },
  plugins: [],
};
