import { createRouter, createWebHashHistory } from "vue-router";
// 公共路由
const routes = [
  {
    path: "/:code",
    component: () => import("../views/home.vue"),
    name: "home",
  },
  {
    path: "/lottery/:code",
    component: () => import("../views/lottery.vue"),
    name: "lottery",
  },
  {
    path: "/lotteryF/:code",
    component: () => import("../views/lotteryF.vue"),
    name: "lotteryF",
  },
  {
    path: "/lotteryS/:code",
    component: () => import("../views/lotteryS.vue"),
    name: "lotteryS",
  },
];

const router = createRouter({
  history: createWebHashHistory(),
  routes,
});
router.beforeEach((to, from, next) => {
  next();

});
router.onError((error) => {
});
export default router;
