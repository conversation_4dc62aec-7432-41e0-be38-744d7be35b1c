<template>
  <a-spin :spinning="loading">
    <div class="container">
      <div class="title">{{ detail?.title }}</div>
      <div class="content w-full h-full">
        <img src="../assets/bg1.jpg" class="bg" alt="" srcset="" />
        <div class="lottery-content">
          <div class="top">
            <img class="img" :src="detail?.image" alt="" srcset="" />
            <!-- <div>
							<h3>618狂欢!</h3>
							<p>幸运大抽奖</p>
							<h4>100%中奖!</h4>
							<div v-text="'第一行\n第二行'"></div>
							<div>{{ "第一行\n第二行" }}</div>
							<pre class="pre">{{ "第一行\n第二行第二行第二行第二行第二行第二行第二行第二行第二行第二行第二行第二行第二行第二行第二行第二行第二行第二行第二行第二行\n第二行" }}</pre>
						</div>
						<div><img src="../assets/huawei.jpg" alt="" /></div> -->
          </div>
          <div class="lottery">
            <LotteryGridVue
              v-model:gifts="gifts"
              v-model:lotteryInfo="lotteryInfo"
              :endTime="detail?.end_time"
            />
          </div>
        </div>
      </div>
      <div class="bind-bg" v-if="!hasToken"></div>
      <div class="bind" v-if="!hasToken">
        <div class="bind-title">请登记</div>
        <div class="">
          <a-form
            :model="formState"
            :rules="rules"
            ref="formRef"
            layout="vertical"
          >
            <a-form-item label="昵称" name="name">
              <a-input
                style="line-height: 40px; height: 40px"
                v-model:value="formState.name"
                placeholder="请输入您的昵称"
              ></a-input>
            </a-form-item>
            <a-form-item label="抽奖凭证" name="phone_number">
              <a-input
                style="line-height: 40px; height: 40px"
                v-model:value="formState.phone_number"
                placeholder="请输入抽奖绑定的身份凭证"
              ></a-input>
            </a-form-item>
          </a-form>
          <div class="bind-button" @click="login">确定</div>
        </div>
      </div>

      <div class="footer">
        <div class="foot-box">
          <pre class="pre">{{ detail?.rule }}</pre>
        </div>
      </div>
    </div>
  </a-spin>
</template>

<script setup>
import {
  computed,
  onMounted,
  onBeforeUnmount,
  reactive,
  ref,
  toRaw,
  watch,
  getCurrentInstance,
} from "vue";
import LotteryGridVue from "../components/LotteryGrid.vue";
import axios from "axios";
import Cookies from "js-cookie";
import { getAssetsFile } from "@/utils.js";
import { useRoute } from "vue-router";
import { regularApiUrl } from "../common/index";
const { proxy } = getCurrentInstance();
const loading = ref(false);
const formRef = ref();
const formState = reactive({
  phone_number: "",
  name: "",
});
const checkphone_number = async (rule, value) => {
  if (!value) {
    return false;
  }
  if (!mobile.test(value)) {
    return Promise.reject("请填写正确的手机号码");
  }
};
const rules = ref({
  phone_number: [
    {
      required: true,
      message: "请填写身份凭证",
      trigger: "blur",
    },
    {
      // validator: checkphone_number,
      // trigger: "blur",
    },
  ],
});
const mobile = /^1[1-9][0-9]{9}$/;
const btnLoading = ref(false);
const isSuccess = ref(false);
const hasToken = computed(() => {
  return isSuccess.value || !!Cookies.get("token");
});
const getDeviceModel = () => {
  const userAgent = navigator.userAgent || navigator.vendor || window.opera;
  // iOS detection
  if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
    if (/iPhone/.test(userAgent)) {
      const match = userAgent.match(/iPhone OS (\d+_\d+)/);
      if (match) {
        // You can add more specific model detection logic here if needed
        return `iPhone (iOS ${match[1].replace("_", ".")})`;
      }
      return "iPhone";
    }
    if (/iPad/.test(userAgent)) {
      const match = userAgent.match(/iPad; CPU OS (\d+_\d+)/);
      if (match) {
        // You can add more specific model detection logic here if needed
        return `iPad (iOS ${match[1].replace("_", ".")})`;
      }
      return "iPad";
    }
  }

  // Android detection
  if (/android/i.test(userAgent)) {
    const match = userAgent.match(/Android\s+([\d.]+);?\s+(\w+)[\s|;]/);
    if (match) {
      return `Android Device (${match[2]} - Android ${match[1]})`;
    }
    return "Android Device";
  }

  // Other detection logic can be added here

  return "Unknown Device";
};
const login = () => {
  formRef.value.validate().then(async (valide) => {
    if (valide) {
      // https://zzapi.mingwork.com/wms/data/add替换成真正的接口
      const res = await axios.post(regularApiUrl + "/api/member/login", {
        member: {
          phone_number: formState.phone_number,
          name: formState.name,
        },
        device: {
          name: getDeviceModel(),
        },
        event: {
          id: detail.value.id,
        },
      });
      // let res = await service.visitorDataSave(toRaw(formState))
      if (res.data.data.token) {
        formRef.value.resetFields();
        btnLoading.value = false;
        isSuccess.value = true;
        enableScroll();
        Cookies.set("token", res.data.data.token, {
          expires: 1,
        });
        await getLotteryHistory();
      } else {
        btnLoading.value = false;
      }
    }
  });
};
const gifts = ref([]);
const route = useRoute();
const detail = ref();
const params = {
  event: {
    code: route.params.code,
  },
};
const getDetail = async () => {
  const res = await axios.post(regularApiUrl + "/api/event/detail", {
    ...params,
  });
  // const res1 = await axios.get(regularApiUrl + "api/event/detail?event.code=" + route.params.code);
  detail.value = res.data.data;
};
const getGifts = async () => {
  const res = await axios.post(regularApiUrl + "/api/event/gifts", {
    ...params,
  });
  gifts.value = res.data.data;
};
const lotteryInfo = ref();
const getLotteryHistory = async () => {
  const token = Cookies.get("token");
  const res = await axios.post(
    regularApiUrl + "/api/event/happy-history",
    {
      ...params,
    },
    {
      headers: {
        Authorization: "Bearer " + token,
      },
    }
  );
  lotteryInfo.value = res.data.data.gift;
};

onMounted(async () => {
  var deviceModel = navigator.userAgent;
  loading.value = true;
  try {
    await getDetail();
    await getGifts();
    if (hasToken.value) {
      await getLotteryHistory();
    }
  } catch (error) {
    console.log(error);
    // proxy.$message.error(error.message || "请求失败，请刷新页面重试");
  }
  if (!hasToken.value) {
    disableScroll();
  }
  loading.value = false;
});

// 定义一个函数来阻止滚动事件
const preventScroll = (event) => {
  event.preventDefault();
};

// 定义一个函数来禁用滚动
const disableScroll = () => {
  document
    .querySelector(".container")
    .addEventListener("wheel", preventScroll, { passive: false });
  document
    .querySelector(".container")
    .addEventListener("touchmove", preventScroll, { passive: false });
};

// 定义一个函数来启用滚动
const enableScroll = () => {
  document
    .querySelector(".container")
    .removeEventListener("wheel", preventScroll);
  document
    .querySelector(".container")
    .removeEventListener("touchmove", preventScroll);
};

// 在组件卸载前移除事件监听器
onBeforeUnmount(() => {
  enableScroll();
});
// 监听 hasToken 的变化并相应地禁用或启用滚动
watch(hasToken.value, (newVal) => {
  if (newVal) {
    enableScroll();
  } else {
    disableScroll();
  }
});
</script>

<style lang="less" scoped>
.container {
  position: relative;
  min-height: 100vh;
  // display: flex;
  flex-direction: column;
  background: transparent;

  .title {
    color: #fff;
    text-align: left;
    padding: 0 12px;
    font-size: 12px;
    line-height: 24px;
    // position: absolute;
    // left: 0;
    // top: 0;
    z-index: 99;
    background: #e4170a;
    width: 100%;
  }

  .bind {
    position: fixed;
    width: 360px;
    height: 340px;
    background: #fff;
    border-radius: 8px;
    left: 50%;
    transform: translateX(-50%);
    top: 55%;
    margin-top: -170px;
    z-index: 11;
    text-align: center;
    color: #222;
    line-height: 32px;
    padding: 24px;

    .bind-title {
      margin-bottom: 24px;
    }
  }

  .bind-bg {
    width: 100vw;
    height: 100vh;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 10;
    background: rgba(0, 0, 0, 0.6);
    overflow: hidden;
  }
}

.lottery-content {
  flex: 1;
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-content: middle;
  height: 100%;
  padding-top: 24px;
}

.content {
  position: relative;
  width: 100%;
  // padding-top: 24px;
  // height: 750px;
  .top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    // margin-bottom: 10px;
    color: #fff;
    padding: 0;
    text-align: left;
    width: 100%;
    line-height: 1.5;
    margin: 0 auto;

    .img {
      width: 100%;
    }

    h3 {
      font-size: 20px;
      margin: 0;
    }

    h4 {
      font-size: 16px;
      margin: 0;
    }

    p {
      font-size: 14px;
      margin: 0;
    }

    img {
      // width: 180px;
      // height: 150px;
    }
  }
}

.bg {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

button:focus,
button:focus-visible {
  outline: none;
}

.footer {
  width: 100%;
  height: 120px;
  background: #e4170a;
  text-align: left;
  color: #fff;

  .foot-box {
    width: 360px;
    margin: 0 auto;
  }

  h4,
  p {
    margin: 0;
    padding: 0;
    color: #fff;
    line-height: 1.7;
  }

  h4 {
    font-size: 14px;
  }

  p {
    font-size: 12px;
  }
}

.ant-btn-default:not(:disabled):hover {
  color: #e4170a;
}

:deep(.ant-form-item-explain-error) {
  text-align: left;
}

.pre {
  white-space: pre-wrap;
  color: #fff;
}

.bind-button {
  width: 100%;
  background-color: rgba(228, 23, 10, 0.8);
  font-weight: bold;
  color: #fff;
  line-height: 40px;
  border-radius: 4px;
}
</style>
