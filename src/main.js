import { createApp } from 'vue'
import './style.css'
import router from "./router/index";
import App from './App.vue'
import "./less/tailwind.css";
import Antd from "ant-design-vue";
import { message, Modal } from "ant-design-vue";
import locale from "ant-design-vue/es/date-picker/locale/zh_CN";
import "ant-design-vue/dist/reset.css";
const app = createApp(App)
app.config.globalProperties.$message = message;
app.config.globalProperties.$Modal = Modal

app.use(Antd);
app.use(router);
app.mount('#app')
