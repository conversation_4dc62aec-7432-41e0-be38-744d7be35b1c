<template>
  <div class="lottery-box">
    <div class="lottery-container">
      <div class="grid">
        <template v-for="(item, index) in gifts" :key="index">
          <div
            v-if="index <= 5"
            :class="['grid-item', { active: activeIndex === index }]"
          >
            <div style="width: 100%">
              <img :src="item.image" alt="" />
              <p>{{ item.name }}</p>
            </div>
          </div>
        </template>
      </div>
    </div>
    <div class="histories-mask" v-if="AllVisible" @click="hideModal"></div>
    <div class="histories-modal" v-if="AllVisible">
      <img src="@/assets/2.png" class="bg" alt="" srcset="" />
      <div class="histories">
        <div class="histories-box">
          <div>
            <img :src="lotteryDetail?.image" alt="" class="result-img" />
            <p>{{ lotteryDetail?.name }}</p>
          </div>
        </div>
        <div class="close" @click="AllVisible = false">关闭</div>
        <div class="fireworks" id="fireworks">
          <img src="@/assets/cj.gif" alt="" srcset="" />
        </div>
      </div>
    </div>
    <div class="success-modal"></div>
  </div>
  <div class="toast" style="margin-top: 20px; color: #666">
    <div>抽奖凭证：{{ member_id }}</div>
    <div style="margin-left: 12px">剩余抽奖次数：{{ remainingTimes }}</div>
  </div>
  <div v-if="isTimeout">
    <div style="font-size: 16px; color: #666; margin: 8px auto">
      活动已结束~
    </div>
  </div>
  <a-button
    class="lottery-button"
    @click="startLottery"
    size="large"
    v-else-if="!lotteryDetail?.name"
    :class="lotteryDetail?.name ? 'has-gift' : ''"
    :disabled="lotteryDetail?.name"
    >{{ lotteryDetail?.name ? "已经抽过了" : "开始抽奖" }}</a-button
  >
  <div></div>
  <div class="logout" @click="logout">离开</div>
  <div class="lottery-result">
    <div class="text" v-if="lotteryDetail?.name">中奖结果</div>
    <img
      class="img"
      v-if="lotteryDetail?.image"
      :src="lotteryDetail?.image"
      alt=""
    />
    <div class="img" v-else>点击开始抽奖，好运马上来</div>
    <div class="text" v-if="lotteryDetail?.name">
      {{ lotteryDetail?.name }}
    </div>
  </div>
</template>

<script>
import axios from "axios";
import Cookies from "js-cookie";
import { getAssetsFile } from "@/utils.js";
import { apiUrl } from "../common/index";
import dayjs from "dayjs";
export default {
  data() {
    return {
      gridItems: Array(6).fill(""),
      activeIndex: -1,
      isRunning: false,
      totalDuration: 3000, // 总时长
      totalSteps: 50, // 总步数
      visible: false,
      btnLoading: false,
      lotteryDetail: {},
      apiUrl,
      AllVisible: false,
      showToast: false,
      remainingTimes: 0,
    };
  },
  computed: {
    isTimeout() {
      const nowTime = dayjs();
      const endTime = dayjs(this.endTime);
      nowTime.diff(endTime);
      return Boolean(nowTime.diff(endTime) > 0);
    },
    member_id() {
      return Cookies.get("member_id");
    },
  },
  props: {
    gifts: {
      type: Array,
      default: () => [],
    },
    lotteryInfo: {
      type: Object,
      default: () => ({}),
    },
    endTime: {
      type: String,
      default: "",
    },
  },
  watch: {
    // 监听lotteryInfo
    lotteryInfo: {
      deep: true,
      immediate: true,
      handler(newVal) {
        this.lotteryDetail = newVal;
      },
    },
  },
  mounted() {
    // this.getLotteryHistory();
  },
  methods: {
    getAssetsFile,
    animations(index) {
      let len = 4 * 2 + index + 1;
      const delay = len * 200;
      // 生成一个数组
      let arr = new Array(len).fill(200);
      let i = 0;
      const timer = setInterval(() => {
        this.activeIndex = i % 6;
        i++;
      }, arr[i]);
      setTimeout(() => {
        clearInterval(timer);
        // this.activeIndex = null;
      }, delay + 100);
      // this.activeIndex = i % 4;
    },
    async startLottery() {
      this.btnLoading = true;
      // 请求接口获取中奖信息
      // /api/event/happy-once
      const token = Cookies.get("token");
      const res = await axios.post(
        // apiUrl + "/api/event/happy-once",
        apiUrl + "/api/member/make-happy",
        {
          event: {
            code: this.$route.params.code,
            // id: this.$route.params.id,  // 新增的昵称
          },
        },
        {
          headers: {
            Authorization: "Bearer " + token,
          },
        }
      );
      if (res.data.data) {
        let result = res.data.data;
        const resIndex = this.gifts.findIndex((item) => item.id === result.id);
        this.animations(resIndex);
        setTimeout(() => {
          this.lotteryDetail = res.data.data;
          let currentStep = 0;
          Cookies.set("hasLottery", true, { expires: 1 });
          if (res.data.data) {
            // this.visible = true;
            this.btnLoading = false;
            this.AllVisible = true;
            this.showToast = true;
          } else {
            console.log("没有中奖");
          }
        }, (4 * 2 + resIndex + 1) * 200);
      } else {
        this.$message.error(res.data.error);
      }
    },

    easeOutCubic(t) {
      return 1 - Math.pow(1 - t, 3);
    },
    logout() {
      Cookies.remove("token");
      Cookies.remove("hasLottery");
      window.location.reload();
    },
    hideModal() {
      this.AllVisible = false;
      this.showToast = false;
    },
  },
};
</script>

<style lang="less" scoped>
.lottery-box {
  width: 370px;
  height: 312px;
  margin: 0 auto;
  background: url(../assets/lo.png) no-repeat;
  background-size: 100% 100%;
  padding-top: 60px;
}

.lottery-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  width: 100%;
}

.grid {
  display: inline-flex;
  width: auto;
  flex-wrap: wrap;
  justify-content: center;
  padding-top: 10px;
  gap: 8px;
}

.grid-item {
  width: 92px;
  height: 92px;
  background-color: #ddd;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20px;
  border: 2px solid #fff;
  border-radius: 4px;
  transition: background-color 0.3s, color 0.3s;
  padding-top: 2px;

  div {
    width: 100%;
    padding: 4px;
  }

  img {
    width: 100%;
    height: 72px;
    object-fit: fill;
    background: #fff;
  }

  p {
    margin: 0;
    font-size: 12px;
    width: 100%;
    line-height: 20px;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.grid-item.active {
  background-color: #f00;
  color: #fff;
}

button:focus,
button:focus-visible {
  outline: none;
}

.lottery-button {
  background: url(../assets/btn.png) no-repeat;
  background-size: 100% 100%;
  border: none;
  outline: none;
  color: #fff;
  font-weight: bold;

  &.has-gift {
    opacity: 0.6;
    filter: grayscale(80%);
    -webkit-filter: grayscale(80%);
    /* Chrome, Safari, Opera */
  }
}

.ant-btn-default:not(:disabled):hover {
  color: #fff;
}

.logout {
  font-size: 12px;
  text-align: center;
  line-height: 20px;
  text-decoration: underline;
  color: #999;
}

.lottery-result {
  width: 370px;
  margin: 20px auto;
  background: #fff;
  padding: 12px;
  border-radius: 8px;
  border: 5px solid #fec460;
  text-align: center;
  color: #222;

  .img {
    height: 160px;
    width: 100%;
    object-fit: contain;
    line-height: 160px;
  }

  .text {
    font-size: 14px;
    color: #222;
  }
}

.modal {
  text-align: center;

  h4 {
    font-size: 16px;
    line-height: 40px;
  }

  img {
    width: 100px;
    height: 100px;
    object-fit: fill;
  }
}
.histories-modal {
  width: 360px;
  height: 420px;
  position: fixed;
  z-index: 10;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);

  .bg {
    width: 100%;
  }
  .modal-title {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 70px;
    font-size: 24px;
    color: #fff;
    z-index: 9;
  }
  .histories {
    width: 264px;
    margin: 0 auto;
    background: #fff;
    padding: 10px;
    border-left: 2px solid rgb(255, 212, 212);
    border-bottom: 2px solid rgb(255, 212, 212);
    border-right: 2px solid rgb(255, 212, 212);
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
  }

  .histories-box {
    font-size: 12px;
    line-height: 24px;
    color: #333;
    .result-img {
      width: 80%;
      height: 140px;
    }
  }

  .close {
    width: 100px;
    height: 32px;
    line-height: 30px;
    border-radius: 16px;
    font-size: 14px;
    text-align: center;
    background: url(../assets/btn1.png) no-repeat;
    background-size: 100% 100%;
    margin: 0 auto;
    margin-top: 12px;
    position: relative;
    z-index: 12;
  }
}

.histories-mask {
  position: fixed;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  left: 0;
  top: 0;
  z-index: 9;
}

.fireworks {
  position: fixed;
  width: 80vw;
  height: 60vh;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  // display: none;
  opacity: 0.2;
  img {
    width: 100%;
  }
}
</style>
