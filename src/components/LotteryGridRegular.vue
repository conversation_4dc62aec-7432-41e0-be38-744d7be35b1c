<template>
  <div class="lottery-box">
    <div class="lottery-container">
      <div class="grid">
        <template v-for="(item, index) in gifts" :key="index">
          <div
            :class="['grid-item', { active: activeIndex === index }]"
            v-if="!item.hidden"
          >
            <div style="width: 100%" class="gift-item">
              <img :src="item.image" alt="" class="img" />
              <div class="info">
                <p class="overflow">{{ item.name }}</p>
                <p class="overflow">{{ item.description }}</p>
              </div>
              <div class="num">x{{ item.total_number }}</div>
            </div>
          </div>
        </template>
      </div>
    </div>

    <div class="success-modal">
      <!-- <a-modal v-model:open="visible" :footer="null">
				<template #title></template>
<div class="modal">
	<p>恭喜中奖！</p>
	<div>
		<img :src="lotteryDetail?.image" alt="" />
		<p>{{ lotteryDetail?.name }}</p>
	</div>
	<p>祝你每天开心～</p>
</div>
</a-modal> -->
    </div>
  </div>
  <!-- {{ lotteryDetail }} -->
  <div v-if="isTimeout">
    <div style="font-size: 16px; color: #666; margin: 8px auto">
      活动已结束~
    </div>
  </div>
  <a-button
    class="lottery-button"
    @click="startLottery"
    size="large"
    style="margin-top: 20px"
    v-else-if="!lotteryDetail?.name"
    :class="lotteryDetail?.name ? 'has-gift' : ''"
    :disabled="lotteryDetail?.name"
    >{{ lotteryDetail?.name ? "已经抽过了" : "参与抽奖" }}</a-button
  >
  <div class="logout" @click="logout">离开</div>
  <div class="logout" @click="lookAll" v-if="lotteryDetail?.code">
    查看中奖结果
  </div>
  <div style="padding: 0">
    <div class="lottery-result">
      <template v-if="lotteryDetail?.name">
        <div v-if="lotteryDetail?.name && lotteryDetail?.name != '暂未开奖'">
          中奖结果
        </div>
        <div v-if="lotteryDetail?.name == '暂未开奖'">
          <div class="img">暂未开奖,请耐心等待</div>
        </div>
        <template v-else>
          <img
            class="img"
            v-if="lotteryDetail?.image"
            :src="lotteryDetail?.image"
            alt=""
          />
          <div class="text" v-if="lotteryDetail?.name">
            {{ lotteryDetail?.name }}
          </div>
          <div class="text" v-if="lotteryDetail?.description">
            {{ lotteryDetail?.description }}
          </div>
        </template>
      </template>
      <template v-else>
        <div class="img">点击参与抽奖，好运马上来</div>
      </template>
    </div>
  </div>
  <div
    class="histories-mask"
    v-if="AllVisible || showToast"
    @click="hideModal"
  ></div>
  <div class="histories-modal" v-if="AllVisible">
    <img src="@/assets/1.png" class="bg" alt="" srcset="" />
    <div class="histories">
      <div class="histories-box">
        <div
          v-for="(item, index) in histories"
          :key="index"
          class="histories-item"
        >
          <div class="name">{{ item.member.name }}</div>
          <div class="gift">{{ item.gift.name }}</div>
        </div>
        <div v-if="!histories.length" class="no-history">
          <div class="noHistory">暂无中奖记录</div>
        </div>
      </div>
      <div style="height: 8px"></div>
      <a-pagination
        v-model:current="current"
        simple
        :defaultPageSize="15"
        :total="total"
        @change="onPageChange"
      />
      <div class="close" @click="AllVisible = false">关闭</div>
    </div>
  </div>
  <!-- <a-modal v-model:open="AllVisible" title="中奖结果" width="360px" :footer="null">
		<div class="histories">
			<div> 恭喜以下中奖者～</div>
			<div v-for="(item, index) in histories" :key="index" class="histories-item">
				<div class="name">{{ item.member.name }}</div>
				<div class="gift">{{ item.gift.name }}</div>
			</div>
		</div>
	</a-modal> -->
  <!-- :class="openAnimation ? 'show' : 'hide'" -->
  <view v-if="showToast" class="toast">
    <div class="close-btn z-20" @click="hideToast">
      <img src="@/assets/cls.png" class="w-5 h-5" alt="" srcset="" />
    </div>
    <view v-if="toast.type == 'success'" class="icon relative z-20"
      ><img src="@/assets/success.png" alt="" srcset="" />
    </view>
    <view v-else class="icon relative z-20"
      ><img src="@/assets/error.png" alt="" srcset=""
    /></view>
    <view class="text z-20 relative">{{ toast.text }}</view>
    <div class="fireworks" id="fireworks">
      <img src="@/assets/cj.gif" alt="" srcset="" />
    </div>
  </view>
</template>

<script>
import axios from "axios";
import Cookies from "js-cookie";
import { getAssetsFile } from "@/utils.js";
import { regularApiUrl } from "../common/index";
import dayjs from "dayjs";
export default {
  components: {},
  data() {
    return {
      gridItems: Array(9).fill(""),
      activeIndex: -1,
      isRunning: false,
      totalDuration: 3000, // 总时长
      totalSteps: 50, // 总步数
      visible: false,
      btnLoading: false,
      lotteryDetail: {},
      regularApiUrl,
      AllVisible: false,
      histories: [],
      showToast: false,
      toast: {
        type: "success",
        text: "",
      },
      openAnimation: false,
      current: 1,
      total: 1,
    };
  },
  computed: {
    isTimeout() {
      const nowTime = dayjs();
      const endTime = dayjs(this.endTime);
      nowTime.diff(endTime);
      return Boolean(nowTime.diff(endTime) > 0);
    },
  },
  props: {
    gifts: {
      type: Array,
      default: () => [],
    },
    lotteryInfo: {
      type: Object,
      default: () => ({}),
    },
    endTime: {
      type: String,
      default: "",
    },
  },
  watch: {
    // 监听lotteryInfo
    lotteryInfo: {
      deep: true,
      immediate: true,
      handler(newVal) {
        this.lotteryDetail = newVal;
      },
    },
  },
  mounted() {
    // this.getLotteryHistory();
  },
  methods: {
    getAssetsFile,
    animations(index) {
      let len = 9 * 2 + index + 1;
      const delay = len * 200;
      // 生成一个数组
      let arr = new Array(len).fill(200);
      let i = 0;
      const timer = setInterval(() => {
        this.activeIndex = i % 9;
        i++;
      }, arr[i]);
      setTimeout(() => {
        clearInterval(timer);
        // this.activeIndex = null;
      }, delay + 100);
      // this.activeIndex = i % 9;
    },
    showFireWorks() {
      const fireworks = document.getElementById("fireworks");
      console.log("[ fireworks ] >", fireworks);
      fireworks.style.display = "block";
      setTimeout(() => {
        fireworks.style.display = "none";
      }, 3000);
    },
    async startLottery() {
      this.btnLoading = true;
      // 请求接口获取中奖信息
      // /api/event/happy-once
      const token = Cookies.get("token");
      const res = await axios.post(
        regularApiUrl + "/api/event/join",
        {
          event: {
            code: this.$route.params.code,
            // id: this.$route.params.id,  // 新增的昵称
          },
        },
        {
          headers: {
            Authorization: "Bearer " + token,
          },
        }
      );
      if (res.data.data) {
        let result = res.data.data;
        const resIndex = this.gifts.findIndex((item) => item.id === result.id);
        // this.animations(resIndex);

        setTimeout(() => {
          this.lotteryDetail = res.data.data;
          let currentStep = 0;
          Cookies.set("hasLotteryRegular", true, { expires: 1 });
          if (res.data.data) {
            this.toast = {
              type: "success",
              text: "参与成功，等待开奖",
            };
            this.showToast = true;
            this.$nextTick(() => {
              this.showFireWorks();
            });
            this.visible = true;
            this.btnLoading = false;
          } else {
          }
        }, (9 * 2 + resIndex + 1) * 200);
      } else {
        if (res.data.message) {
          this.toast = {
            type: "success",
            text: res.data.message,
          };
          this.showToast = true;
          this.$nextTick(() => {
            this.showFireWorks();
          });
        } else {
          this.toast = {
            type: "error",
            text: res.data.error,
          };
          this.showToast = true;
        }
      }
      this.openAnimation = true;
      // setTimeout(() => {
      // 	this.hideModal()
      // }, 1500);
    },

    easeOutCubic(t) {
      return 1 - Math.pow(1 - t, 3);
    },
    logout() {
      Cookies.remove("token");
      Cookies.remove("hasLotteryRegular");
      window.location.reload();
    },
    async lookAll() {
      const token = Cookies.get("token");
      const res = await axios.post(
        regularApiUrl + "/api/event/happy-histories",
        {
          event: {
            code: this.$route.params.code,
          },
          page: this.current,
        },
        {
          headers: {
            Authorization: "Bearer " + token,
          },
        }
      );
      this.histories = res.data.data;
      this.current = res.data.meta.current_page;
      this.total = res.data.meta.total;
      // this.histories = []
      this.AllVisible = true;
    },
    onPageChange(page) {
      console.log("[ page ] >", this.current);
      this.lookAll();
    },
    hideModal() {
      this.AllVisible = false;
      this.showToast = false;
      this.openAnimation = false;
    },
    hideToast() {
      this.showToast = false;
    },
  },
};
</script>

<style lang="less" scoped>
.lottery-box {
  width: 370px;
  height: 412px;
  margin: 0 auto;
  background: url(../assets/lo.png) no-repeat;
  background-size: 100% 100%;
  padding-top: 60px;
}

.lottery-container {
  padding: 12px 42px;
  text-align: center;
  width: 100%;
  height: 320px;
  overflow: hidden;
}

.grid {
  // display: inline-flex;
  // width: auto;
  // flex-wrap: wrap;
  // justify-content: center;
  // padding-top: 10px;
  height: 280px;
  overflow-y: auto;
}

// 注释
.grid-item {
  width: 100%;
  // height: 92px;
  // background-color: #ddd;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20px;
  // border: 2px solid #fff;
  border-radius: 0;
  margin-bottom: 6px;
  transition: background-color 0.3s, color 0.3s;

  & + .grid-item {
    padding-top: 6px;
    border-top: 2px solid rgba(255, 255, 255, 0.2);
  }

  // div {
  // 	width: 100%;
  // 	padding: 4px;
  // }

  .gift-item {
    display: flex;
    align-items: center;
    padding: 2px 0;
    padding-right: 8px;

    .img {
      border-radius: 8px;
    }

    .info {
      margin-left: 8px;
      flex: 1;
      width: 0;
    }

    .num {
      font-size: 12px;
      height: 52px;
      display: flex;
      // flex-direction: column;
      align-items: end;
      line-height: 28px;
      margin-left: 8px;
    }
  }

  img {
    width: 52px;
    height: 52px;
    object-fit: fill;
    background: #fff;
  }

  p {
    margin: 0;
    font-size: 12px;
    width: 100%;
    line-height: 20px;
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

// .grid-item.active {
// 	background-color: #f00;
// 	color: #fff;
// }

button:focus,
button:focus-visible {
  outline: none;
}

.lottery-button {
  background: url(../assets/btn.png) no-repeat;
  background-size: 100% 100%;
  border: none;
  outline: none;
  color: #fff;
  font-weight: bold;
  box-shadow: 0 0 2px 1px rgba(255, 255, 255, 0.7);

  &.has-gift {
    opacity: 0.6;
    filter: grayscale(80%);
    -webkit-filter: grayscale(80%);
    /* Chrome, Safari, Opera */
  }
}

.ant-btn-default:not(:disabled):hover {
  color: #fff;
}

.logout {
  font-size: 12px;
  text-align: center;
  line-height: 20px;
  text-decoration: underline;
  color: #999;
}

.lottery-result {
  width: 370px;
  margin: 20px auto;
  background: #fff;
  padding: 12px;
  border-radius: 8px;
  border: 5px solid #fec460;
  text-align: center;
  color: #222;

  .img {
    height: 160px;
    width: 100%;
    object-fit: contain;
    line-height: 160px;
  }

  .text {
    font-size: 14px;
    color: #222;
  }
}

.modal {
  text-align: center;

  h4 {
    font-size: 16px;
    line-height: 40px;
  }

  img {
    width: 100px;
    height: 100px;
    object-fit: fill;
  }
}

.title {
  text-align: center;
  font-size: 18px;
  margin-bottom: 4px;
}

.histories-item {
  background: rgb(252, 92, 92);
  border-radius: 14px;
  display: flex;
  justify-content: space-between;
  padding: 0 12px;
  color: rgba(255, 255, 255, 0.9);

  & + .histories-item {
    margin-top: 4px;
  }
}

.histories-modal {
  width: 360px;
  height: 420px;
  position: fixed;
  z-index: 10;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);

  .bg {
    width: 100%;
  }

  .histories {
    width: 264px;
    margin: 0 auto;
    background: #fff;
    padding: 10px;
    border-left: 2px solid rgb(255, 212, 212);
    border-bottom: 2px solid rgb(255, 212, 212);
    border-right: 2px solid rgb(255, 212, 212);
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
  }

  .histories-box {
    font-size: 12px;
    line-height: 24px;
    height: 180px;
    overflow-y: auto;
  }

  .close {
    width: 100px;
    height: 32px;
    line-height: 30px;
    border-radius: 16px;
    font-size: 14px;
    text-align: center;
    background: url(../assets/btn1.png) no-repeat;
    background-size: 100% 100%;
    margin: 0 auto;
    margin-top: 12px;
  }
}

.histories-mask {
  position: fixed;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  left: 0;
  top: 0;
  z-index: 9;
}

.toast {
  position: fixed;
  z-index: 11;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  // width: 240px;
  max-width: 240px;
  min-width: 160px;
  height: 140px;
  background: #fff;
  display: inline-flex;
  justify-content: center;
  align-content: center;
  flex-direction: column;
  text-align: left;
  color: #222;
  text-align: center;

  &::after {
    content: "";
    position: absolute;
    left: -12px;
    top: -12px;
    width: calc(100% + 24px);
    height: calc(100% + 24px);
    z-index: -1;
    border: 12px solid #fff;
    border-radius: 8px;
    background: #fff;
    // box-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
  }

  .close-btn {
    position: absolute;
    right: 0;
    top: 0;
    width: 24px;
    height: 24px;
    text-align: center;
    line-height: 24px;
    font-size: 16px;
    color: #999;

    img {
      width: 16px;
      height: 16px;
    }
  }

  .icon {
    width: 40px;
    height: 40px;
    margin: 0 auto;
    margin-bottom: 12px;

    img {
      width: 100%;
    }
  }
}

.overflow {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.no-history {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #999;
  font-size: 14px;
}

.fireworks {
  position: fixed;
  width: 80vw;
  height: 60vh;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  display: none;

  img {
    width: 100%;
  }
}

.relative {
  position: relative;
}

.z-20 {
  z-index: 20;
}

:deep(.ant-pagination-item-link) {
  border: 1px solid #eee !important;
}
</style>
